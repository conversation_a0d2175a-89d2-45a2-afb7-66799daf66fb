{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "c522c70d8958b729279df4ecda413474", "files": {"compare_timestamps_py": {"hash": "1a9e1bfcd10a2b8949cedbaa5884fac2", "index": {"url": "compare_timestamps_py.html", "file": "compare_timestamps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 2, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "create_consolidated_webodm_poses_py": {"hash": "25d06b2aae4bab0a660481c123145ea7", "index": {"url": "create_consolidated_webodm_poses_py.html", "file": "create_consolidated_webodm_poses.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 17, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "create_georeferenced_rgb_py": {"hash": "868d7a901234324017367000108d799f", "index": {"url": "create_georeferenced_rgb_py.html", "file": "create_georeferenced_rgb.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 178, "n_excluded": 12, "n_missing": 178, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "georeference_hsi_pixels_py": {"hash": "a6a4eddebd1f5921c0dd537752286b7a", "index": {"url": "georeference_hsi_pixels_py.html", "file": "georeference_hsi_pixels.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 422, "n_excluded": 17, "n_missing": 422, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "lever_arm_utils_py": {"hash": "9dc66bfa0718c33eabd4dbe529931c3a", "index": {"url": "lever_arm_utils_py.html", "file": "lever_arm_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "logging_config_py": {"hash": "fe1f34a66455e73e375efa85b7f1cf57", "index": {"url": "logging_config_py.html", "file": "logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "main_pipeline_py": {"hash": "0de8061479471db26e724d8a933e5fdc", "index": {"url": "main_pipeline_py.html", "file": "main_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 6, "n_missing": 83, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pipeline_exceptions_py": {"hash": "825aebfb7ff3c9f940c30ee1c1886279", "index": {"url": "pipeline_exceptions_py.html", "file": "pipeline_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "plot_hsi_data_py": {"hash": "5e53c967da82b8cd45d6382ca798205e", "index": {"url": "plot_hsi_data_py.html", "file": "plot_hsi_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 8, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "synchronize_hsi_webodm_py": {"hash": "eee60b1e938019b7c5648a262ad17b2b", "index": {"url": "synchronize_hsi_webodm_py.html", "file": "synchronize_hsi_webodm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 17, "n_missing": 232, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "vectorized_georef_py": {"hash": "722424d2d03df2eb1224fac38647329e", "index": {"url": "vectorized_georef_py.html", "file": "vectorized_georef.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 128, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0___init___py": {"hash": "4636e208e5f498a9a06f2754559b9b3c", "index": {"url": "z_1ab0a9e30448b8e0___init___py.html", "file": "src\\hsi_pipeline\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py": {"hash": "9d7026cdb357185f949dd84b563720be", "index": {"url": "z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html", "file": "src\\hsi_pipeline\\create_consolidated_webodm_poses.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 17, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_create_georeferenced_rgb_py": {"hash": "7aa6a4222e82201ad5ff5a69e7ce2ef5", "index": {"url": "z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html", "file": "src\\hsi_pipeline\\create_georeferenced_rgb.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 178, "n_excluded": 12, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_georeference_hsi_pixels_py": {"hash": "e9db01489fd8b9fa539fb0326782833d", "index": {"url": "z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html", "file": "src\\hsi_pipeline\\georeference_hsi_pixels.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 419, "n_excluded": 17, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_lever_arm_utils_py": {"hash": "6d270b2a1654d1a41107ede2d7bb4f97", "index": {"url": "z_1ab0a9e30448b8e0_lever_arm_utils_py.html", "file": "src\\hsi_pipeline\\lever_arm_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_logging_config_py": {"hash": "fe1f34a66455e73e375efa85b7f1cf57", "index": {"url": "z_1ab0a9e30448b8e0_logging_config_py.html", "file": "src\\hsi_pipeline\\logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_main_pipeline_py": {"hash": "79311808921e4ef36f95269410896a34", "index": {"url": "z_1ab0a9e30448b8e0_main_pipeline_py.html", "file": "src\\hsi_pipeline\\main_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 6, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_pipeline_exceptions_py": {"hash": "c0b3ee11f3c2400581dd40465b355f4e", "index": {"url": "z_1ab0a9e30448b8e0_pipeline_exceptions_py.html", "file": "src\\hsi_pipeline\\pipeline_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c979b7456c4d1cc8___init___py": {"hash": "aab4b2f0ac7a0bc8d685980563edc26c", "index": {"url": "z_c979b7456c4d1cc8___init___py.html", "file": "src\\hsi_pipeline\\plotting\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c979b7456c4d1cc8_plot_hsi_data_py": {"hash": "be244aaa44e9420caf7ad5d78965478d", "index": {"url": "z_c979b7456c4d1cc8_plot_hsi_data_py.html", "file": "src\\hsi_pipeline\\plotting\\plot_hsi_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 8, "n_missing": 107, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py": {"hash": "302c8c26938c6a15e234b8af66041167", "index": {"url": "z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html", "file": "src\\hsi_pipeline\\synchronize_hsi_webodm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 17, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_vectorized_georef_py": {"hash": "774967e27d1e757424369851c6f4df6d", "index": {"url": "z_1ab0a9e30448b8e0_vectorized_georef_py.html", "file": "src\\hsi_pipeline\\vectorized_georef.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "fix_test_patches_py": {"hash": "ff0f9dde8887f1e78521f7a14820a43c", "index": {"url": "fix_test_patches_py.html", "file": "fix_test_patches.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 2, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}